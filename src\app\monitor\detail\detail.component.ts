import { Component, HostListener, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { <PERSON><PERSON><PERSON>ontroller, ModalController } from '@ionic/angular';
import { Feature } from 'ol';
import GeoJSON from 'ol/format/GeoJSON';
import Geometry from 'ol/geom/Geometry';
import LineString from 'ol/geom/LineString';
import Point from 'ol/geom/Point';
import VectorLayer from 'ol/layer/Vector';
import VectorSource from 'ol/source/Vector';
import { Fill, Icon, Stroke, Style } from 'ol/style';
import CircleStyle from 'ol/style/Circle';
import { RequestResult } from 'src/app/@core/base/request-result';
import { MapComponent } from 'src/app/share/map-component/map/map.component';
import { Summary, TaskOnlineUser } from '../class/monitor';
import { MonitorService } from '../monitor.service';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';

@Component({
  selector: 'app-monitor-detail',
  templateUrl: './detail.component.html',
  styleUrls: ['./detail.component.scss'],
})
export class MonitorDetailComponent implements OnInit, OnDestroy {
  @ViewChild('ostMap', { static: false }) ostMap: MapComponent;
  @Input() depCode: string;
  @Input() depName: string;
  // 轨迹图层
  trackVectorLayet: VectorLayer<VectorSource<Geometry>> = new VectorLayer({ source: new VectorSource() });
  //  轨迹线要素
  trackLineFeature: Feature<LineString> = new Feature<LineString>();
  // 实时监控汇总
  taskOnline: Summary = new Summary();
  // 人员列表
  taskOnlineUserList: TaskOnlineUser[] = [];
  // 当前定位人员
  taskOnlineUser: TaskOnlineUser;
  // 下拉刷新事件
  refreshEvent: any;
  // 计时器-刷新人员列表及在线离线人数
  intervalUser: any;
  // 计时器-刷新轨迹
  intervalTrack: any;
  // 倒计时剩余时间（秒）
  countdownTime: number = 60;
  // 倒计时定时器
  countdownInterval: any;
  // 手动刷新状态
  isManualRefreshing: boolean = false;
  // 是否需要自动适应地图视图（仅在切换人员时为true）
  shouldFitMapView: boolean = false;
  // 用于取消所有订阅的信号
  private destroy$: Subject<void> = new Subject<void>();
  constructor(
    private monitorService: MonitorService,
    public modalController: ModalController,
    public alertController: AlertController,
  ) { }

  ngOnInit(): void {
    this.initOnline();
    this.startUserRefreshTimer();
    setTimeout(() => {
      // 初始化轨迹
      this.initTrack();
    }, 500);
  }
  initOnline = () => {
    // 初始化实时监控汇总
    this.initOnlineSummary();
    // 初始化人员列表
    this.initOnlineUserList();
  }

  /**
   * 启动用户刷新定时器和倒计时
   */
  startUserRefreshTimer(): void {
    // 重置倒计时
    this.countdownTime = 60;
    // 启动倒计时定时器（每秒更新一次）
    this.countdownInterval = setInterval(() => {
      this.countdownTime--;
      if (this.countdownTime <= 0) {
        this.countdownTime = 60; // 重置倒计时
      }
    }, 1000);
    // 启动用户刷新定时器（每分钟刷新一次）
    this.intervalUser = setInterval(() => {
      this.initOnline();
      this.countdownTime = 60; // 重置倒计时
    }, 60000);
  }

  /**
   * 重新启动定时器
   */
  restartUserRefreshTimer(): void {
    // 清除现有定时器
    clearInterval(this.intervalUser);
    clearInterval(this.countdownInterval);
    // 重新启动定时器
    this.startUserRefreshTimer();
  }

  /**
   * 手动刷新
   */
  onManualRefresh(): void {
    if (this.isManualRefreshing) {
      return; // 防止重复点击
    }

    this.isManualRefreshing = true;

    // 立即刷新数据
    this.initOnline();

    // 重新启动定时器（重置倒计时）
    this.restartUserRefreshTimer();

    // 1.5秒后恢复按钮状态，给用户足够的视觉反馈
    setTimeout(() => {
      this.isManualRefreshing = false;
    }, 1500);
  }
  /**
   * 初始化轨迹
   */
  initTrack(): void {
    this.createTrackLine();
    this.ostMap.map.addLayer(this.trackVectorLayet);
  }
  /**
   * 创建管线
   */
  createTrackLine(): void {
    this.trackLineFeature = new Feature({
      geometry: new LineString([]),
    });
  }
  /**
   * 创建轨迹
   */
  setTrackByCoordinates(track: Feature<LineString>): void {
    this.trackVectorLayet.getSource().clear();
    this.resetTrack(track.getGeometry());
    if (this.trackLineFeature) {
      this.trackVectorLayet.getSource().addFeature(this.trackLineFeature);
    }
  }
  /**
   * 轨迹重置
   */
  private resetTrack(track: LineString): void {
    if (this.trackLineFeature.getGeometry()) {
      this.trackLineFeature.getGeometry().setCoordinates(track.getCoordinates());
      const trackStyle = [
        new Style({
          fill: new Fill({ color: '#0f0' }),
          stroke: new Stroke({
            color: '#0f0',
            width: 3,
          }),
        }),
        new Style({
          geometry: new Point(this.trackLineFeature.getGeometry().getFirstCoordinate()),
          image: new Icon({
            scale: 0.5,
            offset: [0, 0],
            size: [50, 100],
            src: 'assets/map/monitor/map_track_start.png',
          })
        }),
        new Style({
          geometry: new Point(this.trackLineFeature.getGeometry().getLastCoordinate()),
          image: new CircleStyle({
            radius: 5,
            fill: new Fill({ color: '#248CFF' }),
            stroke: new Stroke({
              color: 'white',
              width: 2,
            }),
          }),
        }),
      ];
      this.trackLineFeature?.setStyle(trackStyle);

      // 只有在切换人员时才自动适应地图视图
      if (this.shouldFitMapView) {
        setTimeout(() => {
          this.ostMap.view.fit(this.trackLineFeature.getGeometry().getExtent(), { padding: [50, 50, 50, 50], maxZoom: 18, duration: 500 });
        }, 250);
        // 重置标志
        this.shouldFitMapView = false;
      }
    }
  }
  /**
   * 获取实时监控汇总
   */
  initOnlineSummary(): void {
    this.monitorService.getOnlineSummary({ depName: this.depName })
      .pipe(takeUntil(this.destroy$))
      .subscribe(this.onOnlineSummarySuccess);
  }
  onOnlineSummarySuccess = (ret: RequestResult<Summary>) => {
    if (ret.code === 0 && ret.data) {
      const summary = ret.data[0];
      this.taskOnline = summary;
    }
  }
  /**
   * 初始化人员列表
   */
  initOnlineUserList(): void {
    this.monitorService.getOnlineUserList({ depCode: this.depCode })
    .pipe(takeUntil(this.destroy$))
    .subscribe(this.onInitOnlineUserListSuccess);
  }
  onInitOnlineUserListSuccess = (ret: RequestResult<TaskOnlineUser[]>) => {

    this.closeRefresh(); // 关闭刷新

    // 重新设置当前定位人的选中状态
    if (this.taskOnlineUser) {
      ret.data = this.transformListSelect(ret);
    }
    if (ret.data) {
      this.taskOnlineUserList = ret.data;
    }
  }
  /**
   * 重新设置当前定位人的选中状态
   */
  transformListSelect = (ret: RequestResult<TaskOnlineUser[]>) => {
    return ret.data.map((item: TaskOnlineUser) => {
      if (item.userCode === this.taskOnlineUser.userCode) {
        item.select = true;
      } else {
        item.select = false;
      }
      return item;
    });
  }
  /**
   * 定位
   */
  onLocation(info: TaskOnlineUser): void {
    // 如果对方没有执行任务中，则进行弹窗提醒
    this.taskOnlineUser = info;
    this.taskOnlineUserList.map((item: TaskOnlineUser) => {
      if (item.userCode === info.userCode) {
        return item.select = true;
      } else {
        return item.select = false;
      }
    });
    // 停止定时器
    clearInterval(this.intervalTrack);
    // 设置标志：切换人员时需要自动适应地图视图
    this.shouldFitMapView = true;
    // 点选不同人员时候，重新设置当前人员轨迹
    this.setCurrentUserTrack();
    // 点击人员可以定位在地图上，并且展示该人员实时轨迹（每10秒刷新一次）
    this.timer();
  }
  /**
   * 设置人员轨迹信息-刷新轨迹
   */
  setCurrentUserTrack = () => {
    this.monitorService.getOnlineTrail({ userCode: this.taskOnlineUser.userCode })
    .pipe(takeUntil(this.destroy$))
    .subscribe(this.getOnlineTrailSuccess);
  }
  getOnlineTrailSuccess = (ret: any) => {
    if (ret.data) {
      const line = new GeoJSON().readFeature(ret.data) as Feature<LineString>;
      // 设置轨迹线
      this.setTrackByCoordinates(line);
    } else {
      // 不显示离线提示，只是停止轨迹定时器
      clearInterval(this.intervalTrack);
    }
  }

  /**
   * 定时器（每10秒刷新一次）
   */
  timer(): void {
    // 启动定时器
    this.intervalTrack = setInterval(this.setCurrentUserTrack, 10000);
  }
  /**
   * 下拉刷新
   */
  doRefresh(event): void {
    this.refreshEvent = event;
    this.initOnlineUserList();
  }
  // 关闭刷新
  closeRefresh(): void {
    if (this.refreshEvent) {
      this.refreshEvent.target.complete();
    }
  }
  /**
   * 回退
   */
  goBack(): void {
    this.modalController.dismiss();
  }
  /**
   * 手机左滑回退
   */
  @HostListener('document:ionBackButton', ['$event'])
  onBackButton($event): void {
    $event.detail.register(100, async () => {
      $event.stopImmediatePropagation();
      this.modalController.dismiss();
    });
  }
  ngOnDestroy(): void {
    // 停止定时器
    clearInterval(this.intervalTrack);
    clearInterval(this.intervalUser);
    clearInterval(this.countdownInterval);
    this.ostMap.map.removeLayer(this.trackVectorLayet);
    this.trackVectorLayet.getSource().clear();
    this.trackLineFeature = undefined;
    this.ostMap = undefined;
    this.destroy$.next(); // 发送完成信号
    this.destroy$.complete(); // 标记Subject完成
  }

}

