@import "../../../theme/variables.scss";

.monitor-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
}

.monitor-top {
    height: 50%;
    position: relative;
    flex-shrink: 0;
}

.monitor-center {
    flex-shrink: 0;
    .title-container {
        background: var(--ion-color-secondary);
        color: var(--ion-color-secondary-contrast);
        font-size: 14px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0px 16px;

        .title {
            flex: 1;
        }

        .countdown-container {
            display: flex;
            align-items: center;
            gap: 8px;

            .countdown {
                font-size: 12px;
                color: var(--ion-color-secondary-contrast);
                opacity: 0.8;
            }

            .refresh-btn {
                background: rgba(255, 255, 255, 0.2);
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 4px;
                padding: 4px 8px;
                font-size: 12px;
                color: var(--ion-color-secondary-contrast);
                cursor: pointer;
                transition: all 0.3s ease;

                &:hover:not(:disabled) {
                    background: rgba(255, 255, 255, 0.3);
                }

                &:disabled {
                    opacity: 0.6;
                    cursor: not-allowed;
                }

                span.rotating {
                    display: inline-block;
                    animation: rotate 1s linear infinite;
                }
            }
        }
    }

    // 保持原有的.title样式以防其他地方使用
    .title {
        background: var(--ion-color-secondary);
        padding: 10px 16px;
        color: var(--ion-color-secondary-contrast);
        font-size: 14px;
    }

    .tips {
        background: #cee1ff;
        font-size: 12px;
        padding: 5px 16px;
        .on-line {
            color: var(--ion-color-success);
        }
        .off-line {
            color: var(--ion-color-danger);
            position: absolute;
            right: 16px;
        }
    }
}
.monitor-bottom {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .user-list-container {
        flex: 1;
        overflow-y: auto;
        padding: 8px;

        .user-grid {
            .user-row {
                display: flex;
                flex-wrap: wrap;
                margin: -4px;

                .user-col {
                    width: 50%;
                    padding: 4px;
                    box-sizing: border-box;
                }
            }
        }
    }

    .select {
        background-color: var(--ion-color-secondary);
        color: #fff;
    }

    .location-success {
        color: var(--ion-color-success);
    }

    .location-primary {
        color: var(--ion-color-primary);
    }

    .user {
        border: 1px solid #eee;
        font-size: 12px;
        height: 40px;
        line-height: 40px;
        position: relative;
        border-radius: 4px;
        margin-bottom: 8px;

        .user-name {
            position: absolute;
            left: 16px;
        }

        .on-location {
            position: absolute;
            right: 16px;
            cursor: pointer;

            ion-icon {
                font-size: 10px;
            }
        }
    }

    .no-data {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 200px;

        .no-data-span {
            margin-top: 16px;
            color: #999;
            font-size: 14px;
        }
    }
}

// 刷新按钮旋转动画
.rotating {
    animation: rotate 1s linear infinite;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

// 深色主题 将背景设置为白色
.ios{--ion-background-color:white}
// 安卓
.md{--ion-background-color:white}